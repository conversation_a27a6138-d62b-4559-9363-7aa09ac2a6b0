import random

class JogoStarWars:
    def __init__(self, conexao):
        self.conexao = conexao
        self.jogador_atual = None
    
    def iniciar(self):
        print("\n=== Star Wars MUD ===")
        while True:
            if not self.jogador_atual:
                self.mostrar_menu()
            else:
                self.loop_jogo()

    def _selecionar_opcao_por_numero(self, titulo, opcoes, prompt):
        print(f"\n{titulo}")
        for i, opcao in enumerate(opcoes, 1):
            print(f"{i}. {opcao[0]}")

        while True: 
            try:
                escolha_num = int(input(f"\n{prompt}"))
                
                if 1 <= escolha_num <= len(opcoes):
                    return opcoes[escolha_num - 1][0]
                else:
                    print("Opção inválida. Por favor, escolha um número da lista.")
            except ValueError:
                print("Entrada inválida. Por favor, digite apenas o número.")

    def mostrar_menu(self):
        print("\n1. Criar personagem")
        print("2. Carregar personagem")
        print("3. Deletar personagem")  
        print("4. Sair")
        
        escolha = input("\nEscolha uma opção: ")
        
        if escolha == "1":
            self.criar_personagem()
        elif escolha == "2":
            self.carregar_personagem()
        elif escolha == "3":
            self.deletar_personagem()  
        elif escolha == "4":
            exit()


    def criar_personagem(self):
        cursor = self.conexao.cursor()
        print("\n=== Criação de Personagem ===")
        
        cursor.execute("SELECT nome_classe FROM Classe")
        classes = cursor.fetchall()
        
        cursor.execute("SELECT nome_planeta FROM Planeta")
        planetas = cursor.fetchall()

        classe_escolhida = self._selecionar_opcao_por_numero(
            "Classes disponíveis:", 
            classes, 
            "Escolha o número da sua classe: "
        )

        planeta_escolhido = self._selecionar_opcao_por_numero(
            "Planetas disponíveis:", 
            planetas, 
            "Escolha o número do seu planeta inicial: "
        )

        try:
            cursor.execute("""
                INSERT INTO Personagem (vida_base, level, dano_base, xp, gcs, nome_classe, nome_planeta) 
                VALUES (100, 1, 10, 0, 1000, %s, %s) RETURNING id_player
            """, (classe_escolhida, planeta_escolhido))
            
            self.jogador_atual = cursor.fetchone()[0]
            self.conexao.commit()
            print(f"\nPersonagem '{classe_escolhida}' criado em '{planeta_escolhido}' com sucesso! ID: {self.jogador_atual}")
            
        except Exception as erro:
            print(f"Erro ao criar personagem: {erro}")
            self.conexao.rollback()

        cursor.close()


    def carregar_personagem(self):
        cursor = self.conexao.cursor()
        cursor.execute("SELECT id_player, nome_classe, nome_planeta FROM Personagem")
        personagens = cursor.fetchall()
        print("\n=== Carregar Personagem ===\n")

        for personagem in personagens:
            print(f"ID: {personagem[0]}, Classe: {personagem[1]}, Planeta: {personagem[2]}")

        id_jogador = input("\nDigite o ID do seu personagem: ")
        cursor = self.conexao.cursor()
        
        try:
            cursor.execute("""
                SELECT id_player, nome_classe, nome_planeta, level, vida_base, gcs 
                FROM Personagem 
                WHERE id_player = %s
            """, (id_jogador,))
            
            dados_jogador = cursor.fetchone()
            if dados_jogador:
                self.jogador_atual = dados_jogador[0]
                print(f"\nBem-vindo de volta!")
                print(f"Classe: {dados_jogador[1]}")
                print(f"Planeta: {dados_jogador[2]}")
                print(f"Level: {dados_jogador[3]}")
                print(f"Vida: {dados_jogador[4]}")
                print(f"GCS: {dados_jogador[5]}")
            else:
                print("Personagem não encontrado!")
                
        except Exception as erro:
            print(f"Erro ao carregar personagem: {erro}")
        
        cursor.close()

    def loop_jogo(self):
        while True:
            print("\n=== Comandos ===")
            print("1. status - Ver status do personagem")
            print("2. viajar - Viajar para outro planeta")
            print("3. missoes - Ver missões disponíveis")
            print("4. combate - Procurar combate")
            print("5. descansar - Recuperar vida")
            print("6. inventario - Ver inventário")
            print("7. sair - Sair do jogo")
            
            comando = input("\n> ").lower().strip()

            if comando == "7":
                self.jogador_atual = None
                print("\nSessão encerrada. Voltando para o menu principal...")
                break
            elif comando == "1":
                self.mostrar_status()
            elif comando == "2":
                self.menu_viagem()
            elif comando == "3":
                self.menu_missoes()
            elif comando == "4":
                self.iniciar_combate()
            elif comando == "5":
                self.descansar()
            elif comando == "6":
                self.mostrar_inventario()
            else:
                print("Comando não reconhecido!")

    def mostrar_status(self):
        cursor = self.conexao.cursor()
        cursor.execute("""
            SELECT nome_classe, nome_planeta, level, vida_base, gcs 
            FROM Personagem 
            WHERE id_player = %s
        """, (self.jogador_atual,))
        
        status = cursor.fetchone()
        print("\n=== Status do Personagem ===")
        print(f"Classe: {status[0]}")
        print(f"Planeta atual: {status[1]}")
        print(f"Level: {status[2]}")
        print(f"Vida: {status[3]}")
        print(f"GCS: {status[4]}")
        cursor.close()

    def deletar_personagem(self):
        cursor = self.conexao.cursor()
        cursor.execute("SELECT id_player, nome_classe, nome_planeta FROM Personagem")
        personagens = cursor.fetchall()
        print("\n=== Deletar Personagem ===\n")
        
        for personagem in personagens:
            print(f"ID: {personagem[0]}, Classe: {personagem[1]}, Planeta: {personagem[2]}")


        id_jogador = input("\nDigite o ID do personagem que deseja deletar: ")
        cursor = self.conexao.cursor()
        
        try:
            cursor.execute("""
                DELETE FROM Personagem 
                WHERE id_player = %s
                RETURNING id_player
            """, (id_jogador,))
            
            if cursor.fetchone():
                self.conexao.commit()
                print(f"Personagem {id_jogador} deletado com sucesso!")
                if self.jogador_atual == int(id_jogador):
                    self.jogador_atual = None
            else:
                print("Personagem não encontrado!")
                
        except Exception as erro:
            print(f"Erro ao deletar personagem: {erro}")
            self.conexao.rollback()
        
        cursor.close()

    def menu_viagem(self):
        # 1. Abrindo o cursor manualmente no início
        cursor = self.conexao.cursor()
        try:
            # Todo o código que usa o cursor fica dentro do bloco 'try'

            # Consulta para mostrar naves do jogador
            cursor.execute("""
                SELECT 
                    n.modelo, n.velocidade, n.capacidade,
                    CASE 
                        WHEN x.modelo IS NOT NULL THEN 'X-WING T-65'
                        WHEN y.modelo IS NOT NULL THEN 'YT-1300'
                        WHEN l.modelo IS NOT NULL THEN 'Lambda Shuttle'
                        WHEN f.modelo IS NOT NULL THEN 'Fregata CR90'
                    END as tipo_nave
                FROM Nave n
                LEFT JOIN X_WING_T65 x ON n.modelo = x.modelo
                LEFT JOIN YT_1300 y ON n.modelo = y.modelo
                LEFT JOIN Lambda_Class_Shuttle l ON n.modelo = l.modelo
                LEFT JOIN Fregata_Corelliana_CR90 f ON n.modelo = f.modelo
                WHERE n.Id_Player = %s
            """, (self.jogador_atual,))
            
            naves = cursor.fetchall()
            if not naves:
                print("\nVocê precisa de uma nave para viajar!")
                return # O 'finally' será executado mesmo com este 'return'
            
            # Mostra naves disponíveis
            print("\n=== Suas Naves ===")
            print("Modelo          | Tipo            | Velocidade | Capacidade")
            print("-" * 60)
            for i, nave in enumerate(naves, 1):
                print(f"{i}. {nave[0]:<13} | {nave[3]:<14} | {nave[1]:<10} | {nave[2]}")
            
            # Pega planeta atual
            cursor.execute("SELECT nome_planeta FROM Personagem WHERE id_player = %s", (self.jogador_atual,))
            planeta_atual = cursor.fetchone()[0]
            
            # Lista planetas e requisitos
            cursor.execute("""
                SELECT p.nome_planeta, p.clima, 
                       CASE 
                           WHEN p.nome_planeta = 'Coruscant' THEN 150
                           WHEN p.nome_planeta = 'Tatooine' THEN 100
                           ELSE 0
                       END as velocidade_minima
                FROM Planeta p
                WHERE p.nome_planeta != %s
            """, (planeta_atual,))
            
            planetas = cursor.fetchall()
            print(f"\nVocê está em: {planeta_atual}")
            print("\nPlanetas disponíveis:")
            for i, planeta in enumerate(planetas, 1):
                req = f"(Requer nave com velocidade {planeta[2]})" if planeta[2] > 0 else "(Sem requisitos)"
                print(f"{i}. {planeta[0]} | Clima: {planeta[1]} {req}")
            
            # Pede o número do planeta e valida
            destino_escolhido = None
            while True:
                try:
                    escolha_planeta = int(input("\nEscolha o número do planeta para o qual deseja viajar: "))
                    if 1 <= escolha_planeta <= len(planetas):
                        destino_escolhido = planetas[escolha_planeta - 1][0]
                        break
                    else:
                        print("Opção inválida. Por favor, escolha um número da lista.")
                except ValueError:
                    print("Entrada inválida. Por favor, digite apenas o número.")

            # Seleção da nave por número
            nave_escolhida = None
            if len(naves) == 1:
                nave_escolhida = naves[0][0]
                print(f"\nUsando sua única nave: {nave_escolhida}")
            else:
                 try:
                    escolha_nave = int(input("\nEscolha o número da nave que deseja usar: ")) - 1
                    if 0 <= escolha_nave < len(naves):
                        nave_escolhida = naves[escolha_nave][0]
                    else:
                        print("Número da nave inválido. Operação cancelada.")
                        return # O 'finally' também será executado aqui
                 except ValueError:
                    print("Entrada inválida. Operação cancelada.")
                    return # E aqui também

            # Chama a função de viajar
            # Como essa função abre seu próprio cursor, não há problema em chamá-la aqui
            self.viajar_para_planeta(destino_escolhido, nave_escolhida)
            
        finally:
            # 2. O bloco 'finally' garante que o cursor será fechado, não importa o que aconteça
            cursor.close()

    def viajar_para_planeta(self, planeta_destino, nave_modelo):
        cursor = self.conexao.cursor()
        
        # Verifica se a nave pertence ao jogador
        cursor.execute("""
            SELECT velocidade 
            FROM Nave 
            WHERE modelo = %s AND Id_Player = %s
        """, (nave_modelo, self.jogador_atual))
        
        nave = cursor.fetchone()
        if not nave:
            print("Você não possui esta nave!")
            return
        
        # Verifica requisitos do planeta
        velocidade_minima = 0
        if planeta_destino == 'Coruscant':
            velocidade_minima = 150
        elif planeta_destino == 'Tatooine':
            velocidade_minima = 100
        
        if nave[0] < velocidade_minima:
            print(f"Sua nave é muito lenta para viajar para {planeta_destino}!");
            print(f"Velocidade mínima necessária: {velocidade_minima}");
            print(f"Velocidade da sua nave: {nave[0]}");
            return
        
        # Resto da lógica de viagem
        try:
            cursor.execute("""
                UPDATE Personagem 
                SET nome_planeta = %s 
                WHERE id_player = %s
            """, (planeta_destino, self.jogador_atual))
            
            self.conexao.commit()
            print(f"\nViagem concluída! Você chegou em {planeta_destino} usando {nave_modelo}")
            
        except Exception as erro:
            print(f"Erro ao viajar: {erro}")
            self.conexao.rollback()
    
        cursor.close()

    def menu_missoes(self):
        """Menu principal de missões"""
        while True:
            print("\n=== Sistema de Missões ===")
            print("1. Ver missões disponíveis")
            print("2. Ver minhas missões")
            print("3. Aceitar missão")
            print("4. Concluir missão")
            print("5. Abandonar missão")
            print("6. Voltar")

            escolha = input("\nEscolha uma opção: ").strip()

            if escolha == "1":
                self.listar_missoes_disponiveis()
            elif escolha == "2":
                self.listar_minhas_missoes()
            elif escolha == "3":
                self.aceitar_missao()
            elif escolha == "4":
                self.concluir_missao()
            elif escolha == "5":
                self.abandonar_missao()
            elif escolha == "6":
                break
            else:
                print("Opção inválida!")

    def listar_missoes_disponiveis(self):
        """Lista missões disponíveis para o jogador atual"""
        cursor = self.conexao.cursor()
        try:
            # Buscar level e planeta do jogador
            cursor.execute("SELECT level, nome_planeta FROM Personagem WHERE id_player = %s", (self.jogador_atual,))
            jogador_info = cursor.fetchone()
            if not jogador_info:
                print("Erro: Jogador não encontrado")
                return

            jogador_level, jogador_planeta = jogador_info

            # Buscar todas as missões do planeta
            cursor.execute("""
                SELECT id_missao, nome_missao, descricao, valor_recompensa,
                       xp_recompensa, level_minimo, tipo_missao
                FROM Missao
                WHERE status = 'Disponível'
                  AND nome_planeta = %s
                  AND level_minimo <= %s
                ORDER BY level_minimo, valor_recompensa
            """, (jogador_planeta, jogador_level))

            todas_missoes = cursor.fetchall()

            # Buscar missões já aceitas pelo jogador
            cursor.execute("SELECT id_missao FROM Missao_Jogador WHERE id_player = %s", (self.jogador_atual,))
            missoes_aceitas = [row[0] for row in cursor.fetchall()]

            # Filtrar missões disponíveis
            missoes_disponiveis = [m for m in todas_missoes if m[0] not in missoes_aceitas]

            if not missoes_disponiveis:
                print(f"\n=== Nenhuma missão disponível em {jogador_planeta} ===")
                print("Viaje para outros planetas ou aumente seu level!")
                return

            print(f"\n=== Missões Disponíveis em {jogador_planeta} ===")
            for missao in missoes_disponiveis:
                print(f"\n[{missao[0]}] {missao[1]}")
                print(f"Descrição: {missao[2]}")
                print(f"Recompensa: {missao[3]} GCS + {missao[4]} XP")
                print(f"Level mínimo: {missao[5]} | Tipo: {missao[6]}")
                print("-" * 50)

        except Exception as erro:
            print(f"Erro ao listar missões: {erro}")
        finally:
            cursor.close()

    def listar_minhas_missoes(self):
        """Lista missões aceitas pelo jogador"""
        cursor = self.conexao.cursor()
        try:
            cursor.execute("""
                SELECT mj.id_missao, m.nome_missao, mj.status_jogador,
                       m.tipo_missao, m.valor_recompensa, m.xp_recompensa
                FROM Missao_Jogador mj
                JOIN Missao m ON mj.id_missao = m.id_missao
                WHERE mj.id_player = %s
                ORDER BY mj.data_aceita DESC
            """, (self.jogador_atual,))

            missoes = cursor.fetchall()

            if not missoes:
                print("\n=== Você não possui missões ===")
                print("Use a opção 1 para ver missões disponíveis!")
                return

            print("\n=== Suas Missões ===")
            for missao in missoes:
                status_icon = "✓" if missao[2] == "Concluída" else "⏳"
                print(f"\n{status_icon} [{missao[0]}] {missao[1]}")
                print(f"Status: {missao[2]}")
                print(f"Tipo: {missao[3]} | Recompensa: {missao[4]} GCS + {missao[5]} XP")
                print("-" * 50)

        except Exception as erro:
            print(f"Erro ao listar suas missões: {erro}")
        finally:
            cursor.close()

    def aceitar_missao(self):
        """Permite ao jogador aceitar uma missão"""
        try:
            missao_id = int(input("\nDigite o ID da missão que deseja aceitar: "))

            cursor = self.conexao.cursor()

            # Verificar se a missão existe e está disponível
            cursor.execute("""
                SELECT m.nome_missao, m.level_minimo, m.nome_planeta, p.level, p.nome_planeta
                FROM Missao m, Personagem p
                WHERE m.id_missao = %s AND p.id_player = %s AND m.status = 'Disponível'
            """, (missao_id, self.jogador_atual))

            resultado = cursor.fetchone()
            if not resultado:
                print("Erro: Missão não encontrada ou não disponível")
                return

            missao_nome, missao_level, missao_planeta, jogador_level, jogador_planeta = resultado

            # Validações
            if jogador_level < missao_level:
                print(f"Erro: Level insuficiente. Necessário: {missao_level}, Atual: {jogador_level}")
                return

            if jogador_planeta != missao_planeta:
                print(f"Erro: Você precisa estar em {missao_planeta} para aceitar esta missão")
                return

            # Verificar se já aceitou
            cursor.execute("SELECT 1 FROM Missao_Jogador WHERE id_player = %s AND id_missao = %s",
                          (self.jogador_atual, missao_id))
            if cursor.fetchone():
                print("Erro: Você já aceitou esta missão")
                return

            # Aceitar a missão
            cursor.execute("""
                INSERT INTO Missao_Jogador (id_player, id_missao, status_jogador)
                VALUES (%s, %s, 'Em Andamento')
            """, (self.jogador_atual, missao_id))

            self.conexao.commit()
            print(f"Sucesso: Missão '{missao_nome}' aceita com sucesso!")

        except ValueError:
            print("Erro: Digite um número válido!")
        except Exception as erro:
            print(f"Erro ao aceitar missão: {erro}")
            self.conexao.rollback()
        finally:
            cursor.close()

    def concluir_missao(self):
        """Permite ao jogador concluir uma missão"""
        try:
            missao_id = int(input("\nDigite o ID da missão que deseja concluir: "))

            cursor = self.conexao.cursor()

            # Verificar se o jogador tem esta missão em andamento
            cursor.execute("""
                SELECT mj.status_jogador, m.valor_recompensa, m.xp_recompensa, m.nome_missao
                FROM Missao_Jogador mj
                JOIN Missao m ON mj.id_missao = m.id_missao
                WHERE mj.id_player = %s AND mj.id_missao = %s
            """, (self.jogador_atual, missao_id))

            resultado = cursor.fetchone()
            if not resultado:
                print("Erro: Você não possui esta missão")
                return

            status_atual, recompensa_gcs, recompensa_xp, missao_nome = resultado

            if status_atual != 'Em Andamento':
                print("Erro: Esta missão não está em andamento")
                return

            # Buscar dados atuais do jogador
            cursor.execute("SELECT gcs, xp, level FROM Personagem WHERE id_player = %s", (self.jogador_atual,))
            _, xp_atual, level_atual = cursor.fetchone()

            # Calcular novo level
            novo_xp = xp_atual + recompensa_xp
            novo_level = max(level_atual, novo_xp // 1000 + 1)

            # Atualizar jogador com recompensas
            cursor.execute("""
                UPDATE Personagem
                SET gcs = gcs + %s, xp = xp + %s, level = %s
                WHERE id_player = %s
            """, (recompensa_gcs, recompensa_xp, novo_level, self.jogador_atual))

            # Marcar missão como concluída
            cursor.execute("""
                UPDATE Missao_Jogador
                SET status_jogador = 'Concluída'
                WHERE id_player = %s AND id_missao = %s
            """, (self.jogador_atual, missao_id))

            self.conexao.commit()

            print(f"Sucesso: Missão '{missao_nome}' concluída!")
            print(f"Recompensas: {recompensa_gcs} GCS, {recompensa_xp} XP")
            if novo_level > level_atual:
                print(f"Level up! Novo level: {novo_level}")

        except ValueError:
            print("Erro: Digite um número válido!")
        except Exception as erro:
            print(f"Erro ao concluir missão: {erro}")
            self.conexao.rollback()
        finally:
            cursor.close()

    def abandonar_missao(self):
        """Permite ao jogador abandonar uma missão"""
        try:
            missao_id = int(input("\nDigite o ID da missão que deseja abandonar: "))

            cursor = self.conexao.cursor()

            # Verificar se o jogador tem esta missão
            cursor.execute("""
                SELECT mj.status_jogador, m.nome_missao
                FROM Missao_Jogador mj
                JOIN Missao m ON mj.id_missao = m.id_missao
                WHERE mj.id_player = %s AND mj.id_missao = %s
            """, (self.jogador_atual, missao_id))

            resultado = cursor.fetchone()
            if not resultado:
                print("Erro: Você não possui esta missão")
                return

            status_atual, missao_nome = resultado

            if status_atual == 'Concluída':
                print("Erro: Não é possível abandonar uma missão concluída")
                return

            # Remover a missão do jogador
            cursor.execute("""
                DELETE FROM Missao_Jogador
                WHERE id_player = %s AND id_missao = %s
            """, (self.jogador_atual, missao_id))

            self.conexao.commit()
            print(f"Missão '{missao_nome}' abandonada")

        except ValueError:
            print("Erro: Digite um número válido!")
        except Exception as erro:
            print(f"Erro ao abandonar missão: {erro}")
            self.conexao.rollback()
        finally:
            cursor.close()

    def iniciar_combate(self):
        """Inicia um combate contra um inimigo aleatório"""
        cursor = self.conexao.cursor()
        try:
            # Buscar inimigo usando a procedure SQL
            cursor.execute("SELECT * FROM buscar_inimigo_combate(%s)", (self.jogador_atual,))
            inimigo_data = cursor.fetchone()

            if not inimigo_data:
                print("\nNenhum inimigo encontrado neste planeta!")
                return

            inimigo_id, inimigo_nome, inimigo_vida, inimigo_dano, inimigo_nivel, xp_recompensa, gcs_recompensa, tipo_mob = inimigo_data

            # Buscar dados do jogador
            cursor.execute("SELECT nome_classe, vida_base, dano_base, level FROM Personagem WHERE id_player = %s",
                          (self.jogador_atual,))
            jogador_classe, jogador_vida, jogador_dano, jogador_level = cursor.fetchone()

            print(f"\n=== COMBATE INICIADO ===")
            print(f"Você encontrou: {inimigo_nome} (Nível {inimigo_nivel})")
            print(f"Vida do inimigo: {inimigo_vida}")
            print(f"Sua vida: {jogador_vida}")
            print("=" * 40)

            # Loop de combate
            vida_atual_jogador = jogador_vida
            vida_atual_inimigo = inimigo_vida
            turno = 1

            while vida_atual_jogador > 0 and vida_atual_inimigo > 0:
                print(f"\n--- Turno {turno} ---")
                print(f"Sua vida: {vida_atual_jogador}/{jogador_vida}")
                print(f"Vida do {inimigo_nome}: {vida_atual_inimigo}/{inimigo_vida}")

                print("\nEscolha sua ação:")
                print("1. Ataque normal")
                print("2. Usar habilidade especial")
                print("3. Tentar fugir")

                acao = input("\n> ").strip()

                if acao == "3":
                    if random.random() < 0.3:  # 30% chance de fuga
                        print("\nVocê conseguiu fugir do combate!")
                        return
                    else:
                        print("\nNão foi possível fugir!")
                        dano_jogador = 0
                else:
                    usar_habilidade = (acao == "2")

                    # Calcular dano do jogador usando procedure SQL
                    cursor.execute("SELECT calcular_dano_jogador(%s, %s, %s)",
                                  (self.jogador_atual, jogador_dano, usar_habilidade))
                    dano_jogador = cursor.fetchone()[0]

                    vida_atual_inimigo -= dano_jogador

                    if usar_habilidade:
                        print(f"\n⚡ Você usou uma habilidade especial de {jogador_classe}!")

                    print(f"💥 Você causou {dano_jogador} de dano!")

                    if vida_atual_inimigo <= 0:
                        print(f"\n🎉 Você derrotou {inimigo_nome}!")
                        break

                # Turno do inimigo
                dano_inimigo = inimigo_dano + random.randint(-2, 5)

                # 15% chance do inimigo usar ataque especial
                if random.random() < 0.15:
                    dano_inimigo = int(dano_inimigo * 1.5)
                    print(f"\n💀 {inimigo_nome} usou um ataque especial!")

                vida_atual_jogador -= dano_inimigo
                print(f"💥 {inimigo_nome} causou {dano_inimigo} de dano em você!")

                turno += 1

            # Resultado do combate
            if vida_atual_jogador <= 0:
                print(f"\n💀 Você foi derrotado por {inimigo_nome}!")
                print("Você perdeu 10% dos seus GCS...")

                # Aplicar penalidade
                cursor.execute("UPDATE Personagem SET gcs = GREATEST(0, gcs * 0.9) WHERE id_player = %s",
                              (self.jogador_atual,))
                self.conexao.commit()
            else:
                # Vitória - aplicar recompensas
                cursor.execute("SELECT * FROM aplicar_recompensas_combate(%s, %s, %s)",
                              (self.jogador_atual, gcs_recompensa, xp_recompensa))
                resultado = cursor.fetchone()
                novo_level, level_up, vida_recuperada = resultado

                print(f"\n🏆 Recompensas: {gcs_recompensa} GCS, {xp_recompensa} XP")

                if level_up:
                    print(f"🌟 LEVEL UP! Novo level: {novo_level}")
                    print(f"❤️ Vida totalmente recuperada!")

                # Processar drops
                cursor.execute("SELECT processar_drops_combate(%s, %s)",
                              (self.jogador_atual, inimigo_id))
                drops = cursor.fetchone()[0]

                if drops != 'Nenhum item':
                    print(f"📦 Itens obtidos: {drops}")
                else:
                    print("📦 Nenhum item foi dropado")

                self.conexao.commit()

        except Exception as erro:
            print(f"Erro durante o combate: {erro}")
            self.conexao.rollback()
        finally:
            cursor.close()

    def descansar(self):
        """Permite ao jogador recuperar vida"""
        cursor = self.conexao.cursor()
        try:
            # Buscar dados do jogador
            cursor.execute("""
                SELECT nome_classe, vida_base, level
                FROM Personagem
                WHERE id_player = %s
            """, (self.jogador_atual,))

            classe, vida_atual, level = cursor.fetchone()
            vida_maxima = 100 + ((level - 1) * 20)

            if vida_atual >= vida_maxima:
                print("\nVocê já está com a vida cheia!")
                return

            # Calcular recuperação baseada na classe
            recuperacao_base = 30
            if classe == 'Jedi':
                recuperacao_base = 40  # Jedi se cura melhor
            elif classe == 'Sith':
                recuperacao_base = 35  # Sith tem regeneração moderada
            elif classe == 'Cacador_de_Recompensas':
                recuperacao_base = 25  # Caçador se cura menos

            nova_vida = min(vida_maxima, vida_atual + recuperacao_base)
            vida_recuperada = nova_vida - vida_atual

            # Atualizar vida
            cursor.execute("""
                UPDATE Personagem
                SET vida_base = %s
                WHERE id_player = %s
            """, (nova_vida, self.jogador_atual))

            self.conexao.commit()

            print(f"\n💤 Você descansou e recuperou {vida_recuperada} pontos de vida!")
            print(f"❤️ Vida atual: {nova_vida}/{vida_maxima}")

            if classe == 'Jedi':
                print("🌟 A Força acelera sua recuperação!")
            elif classe == 'Sith':
                print("⚡ O lado sombrio fortalece seu corpo!")
            elif classe == 'Cacador_de_Recompensas':
                print("🔧 Seus equipamentos médicos ajudam na recuperação!")

        except Exception as erro:
            print(f"Erro ao descansar: {erro}")
            self.conexao.rollback()
        finally:
            cursor.close()

    def mostrar_inventario(self):
        """Mostra o inventário do jogador"""
        cursor = self.conexao.cursor()
        try:
            cursor.execute("""
                SELECT i.nome_item, i.tipo_item, i.valor, ij.quantidade
                FROM Inventario_Jogador ij
                JOIN Item i ON ij.id_item = i.id_item
                WHERE ij.Id_Player = %s
                ORDER BY i.tipo_item, i.nome_item
            """, (self.jogador_atual,))

            itens = cursor.fetchall()

            if not itens:
                print("\n=== Inventário Vazio ===")
                print("Você não possui nenhum item!")
                return

            print("\n=== Seu Inventário ===")
            print("Item                    | Tipo        | Valor | Qtd")
            print("-" * 55)

            valor_total = 0
            for item in itens:
                nome, tipo, valor, quantidade = item
                valor_item_total = valor * quantidade
                valor_total += valor_item_total
                print(f"{nome:<22} | {tipo:<10} | {valor:>5} | {quantidade:>3}")

            print("-" * 55)
            print(f"Valor total do inventário: {valor_total} GCS")

        except Exception as erro:
            print(f"Erro ao mostrar inventário: {erro}")
        finally:
            cursor.close()